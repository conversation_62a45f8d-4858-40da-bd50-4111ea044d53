import React, { useState, useEffect } from "react";
import { QuickLink } from "@/entities/QuickLink";
import {
  Plus, ExternalLink, Edit, Trash2, Link as LinkIcon,
  Globe, Mail, Github, Twitter, Linkedin, Youtube, Instagram,
  Calendar, Clock, BookOpen, FileText, Camera, Music, Settings, Palette
} from "lucide-react";

interface QuickLinkData {
  id: string;
  title: string;
  url: string;
  icon: string;
  color: string;
}

interface FormData {
  title: string;
  url: string;
  icon: string;
  color: string;
}

const iconComponents: { [key: string]: React.ComponentType<{ className?: string }> } = {
  Globe, Mail, Github, Twitter, Linkedin, Youtube, Instagram,
  Calendar, Clock, BookOpen, FileText, Camera, Music, Settings, Palette,
  LinkIcon
};

const iconOptions = [
  'Globe', 'Mail', 'Github', 'Twitter', 'Linkedin', 'Youtube', 'Instagram',
  'Calendar', 'Clock', 'BookOpen', 'FileText', 'Camera', 'Music', 'Settings', 'Palette'
];

const colorOptions = [
  { name: 'Blue', value: 'blue' },
  { name: 'Purple', value: 'purple' },
  { name: 'Green', value: 'green' },
  { name: 'Red', value: 'red' },
  { name: 'Yellow', value: 'yellow' },
  { name: 'Pink', value: 'pink' }
];

export default function QuickLinksWidget() {
  const [links, setLinks] = useState<QuickLinkData[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingLink, setEditingLink] = useState<QuickLinkData | null>(null);
  const [formData, setFormData] = useState<FormData>({
    title: '',
    url: '',
    icon: 'LinkIcon',
    color: 'blue'
  });

  useEffect(() => {
    loadLinks();
  }, []);

  const loadLinks = async () => {
    const data = await QuickLink.list('-created_date');
    setLinks(data);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (editingLink) {
      await QuickLink.update(editingLink.id, formData);
    } else {
      await QuickLink.create(formData);
    }

    setIsDialogOpen(false);
    setEditingLink(null);
    setFormData({ title: '', url: '', icon: 'LinkIcon', color: 'blue' });
    loadLinks();
  };

  const handleEdit = (link: QuickLinkData) => {
    setEditingLink(link);
    setFormData({
      title: link.title,
      url: link.url,
      icon: link.icon || 'LinkIcon',
      color: link.color || 'blue'
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    await QuickLink.delete(id);
    loadLinks();
  };

  const getColorClass = (color: string) => {
    const colorMap: { [key: string]: string } = {
      blue: 'from-blue-500 to-blue-600',
      purple: 'from-purple-500 to-purple-600',
      green: 'from-green-500 to-green-600',
      red: 'from-red-500 to-red-600',
      yellow: 'from-yellow-500 to-yellow-600',
      pink: 'from-pink-500 to-pink-600'
    };
    return colorMap[color] || colorMap.blue;
  };

  return (
    <div className="glass-effect border border-slate-200/20 dark:border-slate-700/20 rounded-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Quick Links</h3>
          <button
            onClick={() => setIsDialogOpen(true)}
            className="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center gap-1 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Add
          </button>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {links.map((link: QuickLinkData) => {
            const IconComponent = iconComponents[link.icon] || LinkIcon;
            return (
              <div key={link.id} className="group relative">
                <a
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`flex flex-col items-center justify-center p-4 rounded-xl bg-gradient-to-br ${getColorClass(link.color)} text-white transition-all duration-200 hover:scale-105 hover:shadow-lg`}
                >
                  <IconComponent className="w-6 h-6 mb-2" />
                  <span className="text-sm font-medium text-center">{link.title}</span>
                  <ExternalLink className="w-3 h-3 absolute top-2 right-2 opacity-70" />
                </a>
                <div className="absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                  <button
                    className="w-6 h-6 bg-white/20 hover:bg-white/30 rounded text-white flex items-center justify-center transition-colors"
                    onClick={() => handleEdit(link)}
                  >
                    <Edit className="w-3 h-3" />
                  </button>
                  <button
                    className="w-6 h-6 bg-white/20 hover:bg-red-500/80 rounded text-white flex items-center justify-center transition-colors"
                    onClick={() => handleDelete(link.id)}
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Modal Dialog */}
      {isDialogOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h2 className="text-lg font-semibold mb-4">
              {editingLink ? 'Edit Link' : 'Add New Link'}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="title" className="block text-sm font-medium mb-1">
                  Title
                </label>
                <input
                  id="title"
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label htmlFor="url" className="block text-sm font-medium mb-1">
                  URL
                </label>
                <input
                  id="url"
                  type="url"
                  value={formData.url}
                  onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Icon</label>
                <select
                  value={formData.icon}
                  onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {iconOptions.map(icon => (
                    <option key={icon} value={icon}>
                      {icon}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Color</label>
                <select
                  value={formData.color}
                  onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {colorOptions.map(color => (
                    <option key={color.value} value={color.value}>
                      {color.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex gap-2 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
                >
                  {editingLink ? 'Update' : 'Add'} Link
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setIsDialogOpen(false);
                    setEditingLink(null);
                    setFormData({ title: '', url: '', icon: 'LinkIcon', color: 'blue' });
                  }}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}